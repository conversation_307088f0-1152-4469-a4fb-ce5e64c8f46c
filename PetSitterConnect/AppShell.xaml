<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="PetSitterConnect.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:PetSitterConnect"
    xmlns:views="clr-namespace:PetSitterConnect.Views"
    Title="PetSitterConnect">

    <!-- Authentication Pages -->
    <ShellContent
        Title="Login"
        ContentTemplate="{DataTemplate views:LoginPage}"
        Route="login" />

    <ShellContent
        Title="Register"
        ContentTemplate="{DataTemplate views:RegisterPage}"
        Route="register" />

    <!-- Main App Pages -->
    <TabBar Route="main" x:Name="MainTabBar">
        <!-- Pet Owner Tab -->
        <ShellContent
            x:Name="FindSittersTab"
            ContentTemplate="{DataTemplate views:PetCareRequestListPage}"
            Route="requests"
            Icon="paw_search.svg">
            <ShellContent.Icon>
                <FontImageSource FontFamily="MaterialIcons" Glyph="🐾" Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
            </ShellContent.Icon>
        </ShellContent>

        <!-- Pet Sitter Tab -->
        <ShellContent
            x:Name="AvailableRequestsTab"
            ContentTemplate="{DataTemplate views:PetCareRequestListPage}"
            Route="requests"
            IsVisible="False">
            <ShellContent.Icon>
                <FontImageSource FontFamily="MaterialIcons" Glyph="🐾" Color="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryDark}}" />
            </ShellContent.Icon>
        </ShellContent>

        <!-- Common Tabs -->
        <ShellContent
            x:Name="BookingsTab"
            ContentTemplate="{DataTemplate views:BookingListPage}"
            Route="bookings">
            <ShellContent.Icon>
                <FontImageSource FontFamily="MaterialIcons" Glyph="📋" Color="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource SecondaryDark}}" />
            </ShellContent.Icon>
        </ShellContent>

        <ShellContent
            x:Name="MessagesTab"
            ContentTemplate="{DataTemplate views:ChatListPage}"
            Route="messages">
            <ShellContent.Icon>
                <FontImageSource FontFamily="MaterialIcons" Glyph="💬" Color="{AppThemeBinding Light={StaticResource Tertiary}, Dark={StaticResource TertiaryDark}}" />
            </ShellContent.Icon>
        </ShellContent>

        <ShellContent
            x:Name="CalendarTab"
            ContentTemplate="{DataTemplate views:CalendarBookingPage}"
            Route="calendar">
            <ShellContent.Icon>
                <FontImageSource FontFamily="MaterialIcons" Glyph="📅" Color="{AppThemeBinding Light={StaticResource PetAccent4}, Dark={StaticResource PetAccent4}}" />
            </ShellContent.Icon>
        </ShellContent>
    </TabBar>

</Shell>
