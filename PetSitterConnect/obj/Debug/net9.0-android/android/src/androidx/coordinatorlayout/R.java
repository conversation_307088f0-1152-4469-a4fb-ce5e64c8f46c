/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.coordinatorlayout;

public final class R {
	public static final class attr {
		public static final int coordinatorLayoutStyle = 0x7f030153;
		public static final int keylines = 0x7f030282;
		public static final int layout_anchor = 0x7f03028f;
		public static final int layout_anchorGravity = 0x7f030290;
		public static final int layout_behavior = 0x7f030291;
		public static final int layout_dodgeInsetEdges = 0x7f0302c2;
		public static final int layout_insetEdge = 0x7f0302cc;
		public static final int layout_keyline = 0x7f0302cd;
		public static final int statusBarBackground = 0x7f03042d;
	}
	public static final class id {
		public static final int bottom = 0x7f080064;
		public static final int end = 0x7f0800bc;
		public static final int left = 0x7f0800fa;
		public static final int none = 0x7f080155;
		public static final int right = 0x7f080186;
		public static final int start = 0x7f0801c2;
		public static final int top = 0x7f0801f0;
	}
	public static final class style {
		public static final int Widget_Support_CoordinatorLayout = 0x7f100472;
	}
	public static final class styleable {
		public static final int[] CoordinatorLayout = new int[] { 0x7f030282, 0x7f03042d };
		public static final int CoordinatorLayout_keylines = 0;
		public static final int CoordinatorLayout_statusBarBackground = 1;
		public static final int[] CoordinatorLayout_Layout = new int[] { 0x010100b3, 0x7f03028f, 0x7f030290, 0x7f030291, 0x7f0302c2, 0x7f0302cc, 0x7f0302cd };
		public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
		public static final int CoordinatorLayout_Layout_layout_anchor = 1;
		public static final int CoordinatorLayout_Layout_layout_anchorGravity = 2;
		public static final int CoordinatorLayout_Layout_layout_behavior = 3;
		public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 4;
		public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
		public static final int CoordinatorLayout_Layout_layout_keyline = 6;
	}
}
