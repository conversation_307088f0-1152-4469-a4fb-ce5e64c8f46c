/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.browser;

public final class R {
	public static final class color {
		public static final int browser_actions_bg_grey = 0x7f050027;
		public static final int browser_actions_divider_color = 0x7f050028;
		public static final int browser_actions_text_color = 0x7f050029;
		public static final int browser_actions_title_color = 0x7f05002a;
	}
	public static final class dimen {
		public static final int browser_actions_context_menu_max_width = 0x7f060052;
		public static final int browser_actions_context_menu_min_padding = 0x7f060053;
	}
	public static final class id {
		public static final int browser_actions_header_text = 0x7f08006a;
		public static final int browser_actions_menu_item_icon = 0x7f08006b;
		public static final int browser_actions_menu_item_text = 0x7f08006c;
		public static final int browser_actions_menu_items = 0x7f08006d;
		public static final int browser_actions_menu_view = 0x7f08006e;
	}
	public static final class layout {
		public static final int browser_actions_context_menu_page = 0x7f0b001c;
		public static final int browser_actions_context_menu_row = 0x7f0b001d;
	}
	public static final class string {
		public static final int copy_toast_msg = 0x7f0f002e;
		public static final int fallback_menu_item_copy_link = 0x7f0f0035;
		public static final int fallback_menu_item_open_in_browser = 0x7f0f0036;
		public static final int fallback_menu_item_share_link = 0x7f0f0037;
	}
	public static final class xml {
		public static final int image_share_filepaths = 0x7f120000;
	}
}
