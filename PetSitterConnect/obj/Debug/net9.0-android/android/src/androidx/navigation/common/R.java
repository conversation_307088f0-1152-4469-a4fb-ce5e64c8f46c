/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.navigation.common;

public final class R {
	public static final class attr {
		public static final int action = 0x7f030002;
		public static final int argType = 0x7f03003e;
		public static final int destination = 0x7f030184;
		public static final int enterAnim = 0x7f0301ba;
		public static final int exitAnim = 0x7f0301c5;
		public static final int launchSingleTop = 0x7f03028a;
		public static final int mimeType = 0x7f030332;
		public static final int nullable = 0x7f030377;
		public static final int popEnterAnim = 0x7f0303a3;
		public static final int popExitAnim = 0x7f0303a4;
		public static final int popUpTo = 0x7f0303a5;
		public static final int popUpToInclusive = 0x7f0303a6;
		public static final int popUpToSaveState = 0x7f0303a7;
		public static final int restoreState = 0x7f0303c9;
		public static final int route = 0x7f0303cf;
		public static final int startDestination = 0x7f03041b;
		public static final int uri = 0x7f0304fd;
	}
	public static final class styleable {
		public static final int[] NavAction = new int[] { 0x010100d0, 0x7f030184, 0x7f0301ba, 0x7f0301c5, 0x7f03028a, 0x7f0303a3, 0x7f0303a4, 0x7f0303a5, 0x7f0303a6, 0x7f0303a7, 0x7f0303c9 };
		public static final int NavAction_android_id = 0;
		public static final int NavAction_destination = 1;
		public static final int NavAction_enterAnim = 2;
		public static final int NavAction_exitAnim = 3;
		public static final int NavAction_launchSingleTop = 4;
		public static final int NavAction_popEnterAnim = 5;
		public static final int NavAction_popExitAnim = 6;
		public static final int NavAction_popUpTo = 7;
		public static final int NavAction_popUpToInclusive = 8;
		public static final int NavAction_popUpToSaveState = 9;
		public static final int NavAction_restoreState = 10;
		public static final int[] NavArgument = new int[] { 0x01010003, 0x010101ed, 0x7f03003e, 0x7f030377 };
		public static final int NavArgument_android_defaultValue = 1;
		public static final int NavArgument_android_name = 0;
		public static final int NavArgument_argType = 2;
		public static final int NavArgument_nullable = 3;
		public static final int[] NavDeepLink = new int[] { 0x010104ee, 0x7f030002, 0x7f030332, 0x7f0304fd };
		public static final int NavDeepLink_action = 1;
		public static final int NavDeepLink_android_autoVerify = 0;
		public static final int NavDeepLink_mimeType = 2;
		public static final int NavDeepLink_uri = 3;
		public static final int[] NavGraphNavigator = new int[] { 0x7f03041b };
		public static final int NavGraphNavigator_startDestination = 0;
		public static final int[] Navigator = new int[] { 0x01010001, 0x010100d0, 0x7f0303cf };
		public static final int Navigator_android_id = 1;
		public static final int Navigator_android_label = 0;
		public static final int Navigator_route = 2;
	}
}
