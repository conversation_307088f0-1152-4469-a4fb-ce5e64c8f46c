{"version": 2, "dgSpecHash": "HvSF6BrJt+g=", "success": true, "projectFilePath": "/Users/<USER>/Documents/augment-projects/PetSitter/PetSitterConnect/PetSitterConnect.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/communitytoolkit.maui/12.0.0/communitytoolkit.maui.12.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/communitytoolkit.maui.core/12.0.0/communitytoolkit.maui.core.12.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/communitytoolkit.mvvm/8.4.0/communitytoolkit.mvvm.8.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/googlegson/2.11.0.5/googlegson.2.11.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/humanizer.core/2.14.1/humanizer.core.2.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.cryptography.internal/9.0.6/microsoft.aspnetcore.cryptography.internal.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.cryptography.keyderivation/9.0.6/microsoft.aspnetcore.cryptography.keyderivation.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.identity.entityframeworkcore/9.0.6/microsoft.aspnetcore.identity.entityframeworkcore.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.bcl.asyncinterfaces/7.0.0/microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.build.framework/17.8.3/microsoft.build.framework.17.8.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.build.locator/1.7.8/microsoft.build.locator.1.7.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.analyzers/3.3.4/microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.common/4.8.0/microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp/4.8.0/microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.csharp.workspaces/4.8.0/microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.workspaces.common/4.8.0/microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.codeanalysis.workspaces.msbuild/4.8.0/microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlite.core/9.0.6/microsoft.data.sqlite.core.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/9.0.6/microsoft.entityframeworkcore.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/9.0.6/microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/9.0.6/microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/9.0.6/microsoft.entityframeworkcore.design.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/9.0.6/microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite/9.0.6/microsoft.entityframeworkcore.sqlite.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite.core/9.0.6/microsoft.entityframeworkcore.sqlite.core.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/9.0.6/microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/9.0.6/microsoft.extensions.caching.memory.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.4/microsoft.extensions.configuration.9.0.4.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.6/microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.6/microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.6/microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/9.0.6/microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.identity.core/9.0.6/microsoft.extensions.identity.core.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.identity.stores/9.0.6/microsoft.extensions.identity.stores.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.6/microsoft.extensions.logging.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.6/microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/9.0.0/microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.6/microsoft.extensions.options.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.6/microsoft.extensions.primitives.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls/9.0.70/microsoft.maui.controls.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.build.tasks/9.0.70/microsoft.maui.controls.build.tasks.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/9.0.70/microsoft.maui.controls.core.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.xaml/9.0.70/microsoft.maui.controls.xaml.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.70/microsoft.maui.core.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.essentials/9.0.70/microsoft.maui.essentials.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.graphics/9.0.70/microsoft.maui.graphics.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.resizetizer/9.0.70/microsoft.maui.resizetizer.9.0.70.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.illink.tasks/9.0.6/microsoft.net.illink.tasks.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/mono.texttemplating/3.0.0/mono.texttemplating.3.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.bundle_e_sqlite3/2.1.10/sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.core/2.1.10/sqlitepclraw.core.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3/2.1.10/sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.android/2.1.10/sqlitepclraw.lib.e_sqlite3.android.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3.ios/2.1.10/sqlitepclraw.lib.e_sqlite3.ios.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.provider.e_sqlite3/2.1.10/sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.provider.internal/2.1.10/sqlitepclraw.provider.internal.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.4.0/system.buffers.4.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.codedom/6.0.0/system.codedom.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/7.0.0/system.collections.immutable.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition/7.0.0/system.composition.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition.attributedmodel/7.0.0/system.composition.attributedmodel.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition.convention/7.0.0/system.composition.convention.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition.hosting/7.0.0/system.composition.hosting.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition.runtime/7.0.0/system.composition.runtime.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.composition.typedparts/7.0.0/system.composition.typedparts.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/7.0.0/system.io.pipelines.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.3/system.memory.4.5.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/7.0.0/system.reflection.metadata.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.runtime.interopservices.nfloat.internal/6.0.1/system.runtime.interopservices.nfloat.internal.6.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.6/system.text.json.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.threading.channels/7.0.0/system.threading.channels.7.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide/*********/xamarin.android.glide.*********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.annotations/*********/xamarin.android.glide.annotations.*********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.disklrucache/*********/xamarin.android.glide.disklrucache.*********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.android.glide.gifdecoder/*********/xamarin.android.glide.gifdecoder.*********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.activity/1.9.3.2/xamarin.androidx.activity.1.9.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.activity.ktx/1.9.3.2/xamarin.androidx.activity.ktx.1.9.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation/1.9.1.2/xamarin.androidx.annotation.1.9.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.experimental/1.4.1.8/xamarin.androidx.annotation.experimental.1.4.1.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.annotation.jvm/1.9.1.2/xamarin.androidx.annotation.jvm.1.9.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat/1.7.0.5/xamarin.androidx.appcompat.1.7.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.appcompat.appcompatresources/1.7.0.5/xamarin.androidx.appcompat.appcompatresources.1.7.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.common/2.2.0.15/xamarin.androidx.arch.core.common.2.2.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.arch.core.runtime/2.2.0.15/xamarin.androidx.arch.core.runtime.2.2.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.browser/1.8.0.8/xamarin.androidx.browser.1.8.0.8.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.cardview/1.0.0.33/xamarin.androidx.cardview.1.0.0.33.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.collection/1.4.5.2/xamarin.androidx.collection.1.4.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.collection.jvm/1.4.5.2/xamarin.androidx.collection.jvm.1.4.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.collection.ktx/1.4.5.2/xamarin.androidx.collection.ktx.1.4.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.concurrent.futures/*******/xamarin.androidx.concurrent.futures.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout/2.2.0.2/xamarin.androidx.constraintlayout.2.2.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.constraintlayout.core/*******/xamarin.androidx.constraintlayout.core.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.coordinatorlayout/********/xamarin.androidx.coordinatorlayout.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.core/1.15.0.2/xamarin.androidx.core.1.15.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.core.core.ktx/1.15.0.2/xamarin.androidx.core.core.ktx.1.15.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.cursoradapter/1.0.0.31/xamarin.androidx.cursoradapter.1.0.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.customview/1.1.0.30/xamarin.androidx.customview.1.1.0.30.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.customview.poolingcontainer/1.0.0.17/xamarin.androidx.customview.poolingcontainer.1.0.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.documentfile/1.0.1.31/xamarin.androidx.documentfile.1.0.1.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.drawerlayout/1.2.0.15/xamarin.androidx.drawerlayout.1.2.0.15.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.dynamicanimation/1.0.0.31/xamarin.androidx.dynamicanimation.1.0.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2/1.5.0.3/xamarin.androidx.emoji2.1.5.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.emoji2.viewshelper/1.5.0.3/xamarin.androidx.emoji2.viewshelper.1.5.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.exifinterface/1.3.7.9/xamarin.androidx.exifinterface.1.3.7.9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.fragment/1.8.5.2/xamarin.androidx.fragment.1.8.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.fragment.ktx/1.8.5.2/xamarin.androidx.fragment.ktx.1.8.5.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.interpolator/1.0.0.31/xamarin.androidx.interpolator.1.0.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.legacy.support.core.utils/1.0.0.31/xamarin.androidx.legacy.support.core.utils.1.0.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.common/2.8.7.2/xamarin.androidx.lifecycle.common.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.common.jvm/2.8.7.2/xamarin.androidx.lifecycle.common.jvm.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata/2.8.7.2/xamarin.androidx.lifecycle.livedata.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core/2.8.7.2/xamarin.androidx.lifecycle.livedata.core.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.livedata.core.ktx/2.8.7.2/xamarin.androidx.lifecycle.livedata.core.ktx.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.process/2.8.7.2/xamarin.androidx.lifecycle.process.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime/2.8.7.2/xamarin.androidx.lifecycle.runtime.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.android/2.8.7.2/xamarin.androidx.lifecycle.runtime.android.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx/2.8.7.2/xamarin.androidx.lifecycle.runtime.ktx.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.runtime.ktx.android/2.8.7.2/xamarin.androidx.lifecycle.runtime.ktx.android.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel/2.8.7.2/xamarin.androidx.lifecycle.viewmodel.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.android/2.8.7.2/xamarin.androidx.lifecycle.viewmodel.android.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodel.ktx/2.8.7.2/xamarin.androidx.lifecycle.viewmodel.ktx.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.lifecycle.viewmodelsavedstate/2.8.7.2/xamarin.androidx.lifecycle.viewmodelsavedstate.2.8.7.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.loader/1.1.0.31/xamarin.androidx.loader.1.1.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.localbroadcastmanager/*******9/xamarin.androidx.localbroadcastmanager.*******9.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.common/2.8.5.1/xamarin.androidx.navigation.common.2.8.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.fragment/2.8.5.1/xamarin.androidx.navigation.fragment.2.8.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.runtime/2.8.5.1/xamarin.androidx.navigation.runtime.2.8.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.navigation.ui/2.8.5.1/xamarin.androidx.navigation.ui.2.8.5.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.print/1.0.0.31/xamarin.androidx.print.1.0.0.31.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.profileinstaller.profileinstaller/1.4.1.2/xamarin.androidx.profileinstaller.profileinstaller.1.4.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.recyclerview/1.3.2.10/xamarin.androidx.recyclerview.1.3.2.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.resourceinspection.annotation/1.0.1.19/xamarin.androidx.resourceinspection.annotation.1.0.1.19.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate/********/xamarin.androidx.savedstate.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.savedstate.savedstate.ktx/********/xamarin.androidx.savedstate.savedstate.ktx.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.security.securitycrypto/*******-alpha06/xamarin.androidx.security.securitycrypto.*******-alpha06.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.slidingpanelayout/********/xamarin.androidx.slidingpanelayout.********.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.startup.startupruntime/*******/xamarin.androidx.startup.startupruntime.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.swiperefreshlayout/*******4/xamarin.androidx.swiperefreshlayout.*******4.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.tracing.tracing/*******/xamarin.androidx.tracing.tracing.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.transition/*******/xamarin.androidx.transition.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable/*******/xamarin.androidx.vectordrawable.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.vectordrawable.animated/*******/xamarin.androidx.vectordrawable.animated.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.versionedparcelable/*******/xamarin.androidx.versionedparcelable.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager/*******/xamarin.androidx.viewpager.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.viewpager2/*******/xamarin.androidx.viewpager2.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.window/*******/xamarin.androidx.window.*******.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.androidx.window.extensions.core.core/1.0.0.13/xamarin.androidx.window.extensions.core.core.1.0.0.13.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.android.material/1.12.0.2/xamarin.google.android.material.1.12.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.code.findbugs.jsr305/3.0.2.18/xamarin.google.code.findbugs.jsr305.3.0.2.18.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.crypto.tink.android/1.16.0.1/xamarin.google.crypto.tink.android.1.16.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.errorprone.annotations/2.36.0.1/xamarin.google.errorprone.annotations.2.36.0.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.google.guava.listenablefuture/1.0.0.26/xamarin.google.guava.listenablefuture.1.0.0.26.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.jetbrains.annotations/26.0.1.2/xamarin.jetbrains.annotations.26.0.1.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlin.stdlib/2.0.21.2/xamarin.kotlin.stdlib.2.0.21.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu/0.26.1.1/xamarin.kotlinx.atomicfu.0.26.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.atomicfu.jvm/0.26.1.1/xamarin.kotlinx.atomicfu.jvm.0.26.1.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.android/1.9.0.2/xamarin.kotlinx.coroutines.android.1.9.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core/1.9.0.2/xamarin.kotlinx.coroutines.core.1.9.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.coroutines.core.jvm/1.9.0.2/xamarin.kotlinx.coroutines.core.jvm.1.9.0.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core/1.7.3.2/xamarin.kotlinx.serialization.core.1.7.3.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/xamarin.kotlinx.serialization.core.jvm/1.7.3.2/xamarin.kotlinx.serialization.core.jvm.1.7.3.2.nupkg.sha512"], "logs": []}